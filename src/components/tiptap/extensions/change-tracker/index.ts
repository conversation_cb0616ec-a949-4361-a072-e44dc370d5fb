import { <PERSON>lugin, Plugin<PERSON><PERSON> } from "@tiptap/pm/state";
import { Decoration, DecorationSet } from "@tiptap/pm/view";
import { Extension, Mark, mergeAttributes } from "@tiptap/core";
import { Step, StepMap, Transform } from "@tiptap/pm/transform";

// Type definitions for step properties
interface ReplaceStep extends Step {
  jsonID: string;
  from: number;
  to: number;
}

// Constants
const MARK_INSERTION = "insertion";
const MARK_DELETION = "deletion";
const EXTENSION_NAME = "trackchange";

// Data structures for tracking changes
class Span {
  constructor(
    public from: number,
    public to: number,
    public commit: number | null,
    public isDeleted: boolean = false,
    public deletedContent?: string
  ) {}
}

class Commit {
  constructor(
    public message: string,
    public time: Date,
    public steps: Step[],
    public maps: StepMap[],
    public hidden?: boolean
  ) {}
}

class TrackState {
  constructor(
    public blameMap: Span[],
    public commits: Commit[],
    public uncommittedSteps: Step[],
    public uncommittedMaps: StepMap[]
  ) {}

  // Apply a transform to this state
  applyTransform(transform: Transform): TrackState {
    // Invert the steps in the transaction, to be able to save them in the next commit
    const inverted = transform.steps.map((step, i) =>
      step.invert(transform.docs[i])
    );
    const newBlame = updateBlameMap(
      this.blameMap,
      transform,
      this.commits.length
    );
    // Create a new state—since these are part of the editor state, a
    // persistent data structure, they must not be mutated.
    return new TrackState(
      newBlame,
      this.commits,
      this.uncommittedSteps.concat(inverted),
      this.uncommittedMaps.concat(transform.mapping.maps)
    );
  }

  // When a transaction is marked as a commit, this is used to put any
  // uncommitted steps into a new commit.
  applyCommit(message: string, time: Date): TrackState {
    if (this.uncommittedSteps.length == 0) return this;

    const commit = new Commit(
      message,
      time,
      this.uncommittedSteps,
      this.uncommittedMaps,
      false
    );
    return new TrackState(this.blameMap, this.commits.concat(commit), [], []);
  }
}

function updateBlameMap(map: Span[], transform: Transform, id: number): Span[] {
  const result: Span[] = [];
  const mapping = transform.mapping;

  // First, capture deleted content from the original document
  const deletedSpans: Span[] = [];
  for (let stepIndex = 0; stepIndex < transform.steps.length; stepIndex++) {
    const step = transform.steps[stepIndex];
    const doc = transform.docs[stepIndex];

    // Check if this is a replace step that deletes content
    const replaceStep = step as ReplaceStep;
    if (replaceStep.jsonID === "replace" && replaceStep.from < replaceStep.to) {
      const from = replaceStep.from;
      const to = replaceStep.to;

      // Find spans that overlap with the deleted range
      for (const span of map) {
        const overlapStart = Math.max(span.from, from);
        const overlapEnd = Math.min(span.to, to);

        if (overlapStart < overlapEnd) {
          // This span was partially or fully deleted
          const deletedText = doc.textBetween(overlapStart, overlapEnd);
          deletedSpans.push(
            new Span(from, from, span.commit, true, deletedText)
          );
        }
      }
    }
  }

  // Handle existing spans - map them to new positions or mark as deleted
  for (let i = 0; i < map.length; i++) {
    const span = map[i];
    const from = mapping.map(span.from, 1);
    const to = mapping.map(span.to, -1);

    if (from < to) {
      // Span still exists, update its position
      result.push(
        new Span(from, to, span.commit, span.isDeleted, span.deletedContent)
      );
    }
  }

  // Add deleted spans
  result.push(...deletedSpans);

  // Handle new insertions
  for (let i = 0; i < mapping.maps.length; i++) {
    const stepMap = mapping.maps[i];
    const after = mapping.slice(i + 1);
    stepMap.forEach((_s: number, _e: number, start: number, end: number) => {
      insertIntoBlameMap(result, after.map(start, 1), after.map(end, -1), id);
    });
  }

  return result;
}

function insertIntoBlameMap(
  map: Span[],
  from: number,
  to: number,
  commit: number
): void {
  if (from >= to) return;
  let pos = 0;
  let next: Span;
  for (; pos < map.length; pos++) {
    next = map[pos];
    if (next.commit == commit && !next.isDeleted) {
      if (next.to >= from) break;
    } else if (next.to > from) {
      // Different commit, not before
      if (next.from < from) {
        // Sticks out to the left (loop below will handle right side)
        const left = new Span(next.from, from, next.commit, next.isDeleted);
        if (next.to > to) map.splice(pos++, 0, left);
        else map[pos++] = left;
      }
      break;
    }
  }

  while ((next = map[pos])) {
    if (next.commit == commit && !next.isDeleted) {
      if (next.from > to) break;
      from = Math.min(from, next.from);
      to = Math.max(to, next.to);
      map.splice(pos, 1);
    } else {
      if (next.from >= to) break;
      if (next.to > to) {
        map[pos] = new Span(to, next.to, next.commit, next.isDeleted);
        break;
      } else {
        map.splice(pos, 1);
      }
    }
  }

  map.splice(pos, 0, new Span(from, to, commit, false, undefined));
}

// Plugin key for the track changes plugin
const trackPluginKey = new PluginKey("trackChanges");

// Main tracking plugin with proper batching
const trackPlugin = new Plugin({
  key: trackPluginKey,
  state: {
    init(_, instance) {
      return new TrackState(
        [new Span(0, instance.doc.content.size, null, false, undefined)],
        [],
        [],
        []
      );
    },
    apply(tr, tracked) {
      // Only track changes when explicitly committing via Ctrl+S or save button
      const commitMessage = tr.getMeta(trackPluginKey);
      if (commitMessage) {
        // Commit all accumulated changes at once
        tracked = tracked.applyCommit(commitMessage, new Date(tr.time));
      } else if (tr.docChanged) {
        // Accumulate changes but don't create individual steps for each keystroke
        // This batches all changes until a commit is made
        tracked = tracked.applyTransform(tr);
      }
      return tracked;
    },
  },
});

// Plugin for highlighting changes
const highlightPluginKey = new PluginKey("highlightChanges");

// Helper function to calculate diff between two commits
function calculateCommitDiff(
  trackState: TrackState,
  currentCommit: Commit
): Decoration[] {
  const commits = trackState.commits;
  const currentIndex = commits.indexOf(currentCommit);

  if (currentIndex === -1) return [];

  const decorations: Decoration[] = [];

  // Show spans that belong to the current commit
  const currentSpans = trackState.blameMap.filter(
    (span) => span.commit === currentIndex
  );

  currentSpans.forEach((span) => {
    if (span.isDeleted && span.deletedContent) {
      // Show deletions as widgets at the deletion point with actual deleted content
      decorations.push(
        Decoration.widget(span.from, () => {
          const element = document.createElement("span");
          element.className = "commit-diff-deletion";
          element.setAttribute("data-commit-message", currentCommit.message);
          element.setAttribute(
            "data-commit-time",
            currentCommit.time.toISOString()
          );
          element.textContent = span.deletedContent || "[deleted content]";
          element.title = `Deleted: "${span.deletedContent}" in: ${currentCommit.message}`;
          element.style.textDecoration = "line-through";
          element.style.backgroundColor = "#f8d7da";
          element.style.color = "#721c24";
          return element;
        })
      );
    } else if (!span.isDeleted) {
      // Show additions as inline decorations
      decorations.push(
        Decoration.inline(span.from, span.to, {
          class: "commit-diff-addition",
          "data-commit-message": currentCommit.message,
          "data-commit-time": currentCommit.time.toISOString(),
        })
      );
    }
  });

  return decorations;
}

const highlightPlugin = new Plugin({
  key: highlightPluginKey,
  state: {
    init() {
      return { deco: DecorationSet.empty, commit: null };
    },
    apply(tr, prev, oldState, state) {
      const highlight = tr.getMeta(highlightPluginKey);
      if (highlight && highlight.add != null && prev.commit != highlight.add) {
        const tState = trackPlugin.getState(oldState);
        const decos = calculateCommitDiff(tState, highlight.add);
        return {
          deco: DecorationSet.create(state.doc, decos),
          commit: highlight.add,
        };
      } else if (
        highlight &&
        (highlight.clear === true || highlight.clear != null)
      ) {
        return { deco: DecorationSet.empty, commit: null };
      } else if (tr.docChanged && prev.commit) {
        return { deco: prev.deco.map(tr.mapping, tr.doc), commit: prev.commit };
      } else {
        return prev;
      }
    },
  },
  props: {
    decorations(state) {
      return this.getState(state).deco;
    },
  },
});

// Insertion mark for tracking additions
export const InsertionMark = Mark.create({
  name: MARK_INSERTION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "ins" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "ins",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #d4edda; text-decoration: none;",
      }),
      0,
    ];
  },
});

// Deletion mark for tracking deletions
export const DeletionMark = Mark.create({
  name: MARK_DELETION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "del" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "del",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #f8d7da; text-decoration: line-through;",
      }),
      0,
    ];
  },
});

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    trackchange: {
      /**
       * Commit current changes with a message
       */
      commitChanges: (message: string) => ReturnType;
      /**
       * Check if there are uncommitted changes
       */
      hasUncommittedChanges: () => ReturnType;
      /**
       * Get the current track state
       */
      getTrackState: () => ReturnType;
      /**
       * Highlight a specific commit
       */
      highlightCommit: (commit: Commit | null) => ReturnType;
      /**
       * Clear all highlights
       */
      clearAllHighlights: () => ReturnType;
      /**
       * Restore latest content without highlights
       */
      restoreLatestContent: () => ReturnType;
      /**
       * Save document (alias for commitChanges)
       */
      saveDocument: (message?: string) => ReturnType;
    };
  }
}

// Main extension
export const TrackChangeExtension = Extension.create<{
  enabled: boolean;
  userId?: string;
  userName?: string;
}>({
  name: EXTENSION_NAME,

  addOptions() {
    return {
      enabled: true,
      userId: "",
      userName: "",
    };
  },

  addExtensions() {
    return [InsertionMark, DeletionMark];
  },

  addProseMirrorPlugins() {
    return [trackPlugin, highlightPlugin];
  },

  addCommands() {
    return {
      commitChanges:
        (message: string) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta(trackPluginKey, message);
            dispatch(tr);
          }
          return true;
        },

      hasUncommittedChanges:
        () =>
        ({ state }) => {
          const trackState = trackPlugin.getState(state);
          return trackState?.uncommittedSteps.length > 0;
        },

      getTrackState:
        () =>
        ({ state, editor }) => {
          const trackState = trackPlugin.getState(state);
          // Store the track state in the editor's storage for external access
          if (editor && trackState) {
            editor.storage.trackchange.currentTrackState = trackState;
          }
          return true;
        },

      highlightCommit:
        (commit: Commit | null) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            if (commit) {
              tr.setMeta(highlightPluginKey, { add: commit });
            } else {
              tr.setMeta(highlightPluginKey, { clear: true });
            }
            dispatch(tr);
          }
          return true;
        },

      clearAllHighlights:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta(highlightPluginKey, { clear: true });
            dispatch(tr);
          }
          return true;
        },

      restoreLatestContent:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            // Clear highlighting and force a clean state
            tr.setMeta(highlightPluginKey, { clear: true });
            dispatch(tr);
          }
          return true;
        },

      saveDocument:
        (message?: string) =>
        ({ tr, dispatch, state }) => {
          const trackState = trackPlugin.getState(state);
          if (trackState?.uncommittedSteps.length > 0) {
            if (dispatch) {
              tr.setMeta(trackPluginKey, message || "Document saved");
              dispatch(tr);
            }
            return true;
          }
          return false;
        },
    };
  },

  addStorage() {
    return {
      hasUncommittedChanges: false,
      currentTrackState: null,
    };
  },

  onTransaction({ editor }) {
    // Update storage to track if there are uncommitted changes
    const trackState = trackPlugin.getState(editor.state);
    this.storage.hasUncommittedChanges =
      trackState?.uncommittedSteps.length > 0;
  },
});

// Export types and classes for external use
export { Commit, TrackState, Span };

// Export utility functions
export * from "./utils";

export default TrackChangeExtension;
